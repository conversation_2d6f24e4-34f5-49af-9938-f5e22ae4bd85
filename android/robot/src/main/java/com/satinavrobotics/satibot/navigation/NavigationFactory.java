package com.satinavrobotics.satibot.navigation;

import com.satinavrobotics.satibot.vehicle.Vehicle;

/**
 * Factory class for creating and configuring navigation controllers and strategies.
 * This provides a simple API for setting up the unified navigation system.
 */
public class NavigationFactory {
    
    /**
     * Create a fully configured UnifiedNavigationController with default settings
     * 
     * @param vehicle The vehicle to control
     * @param waypointsManager The waypoints manager
     * @return Configured UnifiedNavigationController
     */
    public static UnifiedNavigationController createDefaultNavigationController(Vehicle vehicle, WaypointsManager waypointsManager) {
        UnifiedNavigationController controller = new UnifiedNavigationController(vehicle, waypointsManager);
        
        // Add the combined strategy (highest priority)
        CombinedNavigationStrategy combinedStrategy = createDefaultCombinedStrategy();
        controller.addStrategy(combinedStrategy);
        
        // Add fallback strategies
        ObstacleAvoidanceStrategy obstacleStrategy = createDefaultObstacleAvoidanceStrategy();
        WaypointFollowingStrategy waypointStrategy = createDefaultWaypointFollowingStrategy();
        
        controller.addStrategy(obstacleStrategy);
        controller.addStrategy(waypointStrategy);
        
        return controller;
    }
    
    /**
     * Create a combined navigation strategy with default settings
     */
    public static CombinedNavigationStrategy createDefaultCombinedStrategy() {
        return new CombinedNavigationStrategy();
    }
    
    /**
     * Create an obstacle avoidance strategy with default settings
     */
    public static ObstacleAvoidanceStrategy createDefaultObstacleAvoidanceStrategy() {
        ObstacleAvoidanceStrategy strategy = new ObstacleAvoidanceStrategy();
        strategy.setTraversabilityCostWeight(3.0f);
        strategy.setHeadingDeviationCostWeight(1.0f);
        strategy.setCostBasedNavigationEnabled(true);
        return strategy;
    }
    
    /**
     * Create a waypoint following strategy with default settings
     */
    public static WaypointFollowingStrategy createDefaultWaypointFollowingStrategy() {
        return new WaypointFollowingStrategy();
    }
    
    /**
     * Create an obstacle avoidance strategy with custom settings
     */
    public static ObstacleAvoidanceStrategy createObstacleAvoidanceStrategy(
            float traversabilityCostWeight, 
            float headingDeviationCostWeight, 
            boolean costBasedEnabled) {
        ObstacleAvoidanceStrategy strategy = new ObstacleAvoidanceStrategy();
        strategy.setTraversabilityCostWeight(traversabilityCostWeight);
        strategy.setHeadingDeviationCostWeight(headingDeviationCostWeight);
        strategy.setCostBasedNavigationEnabled(costBasedEnabled);
        return strategy;
    }
    
    /**
     * Create a simple navigation controller with only obstacle avoidance (no waypoints)
     */
    public static UnifiedNavigationController createObstacleAvoidanceOnlyController(Vehicle vehicle) {
        UnifiedNavigationController controller = new UnifiedNavigationController(vehicle, WaypointsManager.getInstance());
        
        ObstacleAvoidanceStrategy obstacleStrategy = createDefaultObstacleAvoidanceStrategy();
        controller.addStrategy(obstacleStrategy);
        
        return controller;
    }
    
    /**
     * Create a simple navigation controller with only waypoint following (no obstacle avoidance)
     */
    public static UnifiedNavigationController createWaypointOnlyController(Vehicle vehicle, WaypointsManager waypointsManager) {
        UnifiedNavigationController controller = new UnifiedNavigationController(vehicle, waypointsManager);
        
        WaypointFollowingStrategy waypointStrategy = createDefaultWaypointFollowingStrategy();
        controller.addStrategy(waypointStrategy);
        
        return controller;
    }
}
